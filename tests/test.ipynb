import requests

url = "https://api.github.com/repos/MrLeritaite/NaturalLanguageProcessing/pulls/4.diff"
headers = {
    "Authorization": "Bearer ****************************************",
    "Accept": "application/vnd.github.v3.diff",
}
r = requests.get(url, headers=headers)
print(r.status_code)
print(r.text[:200])


url = "https://api.github.com/app/installations/********/access_tokens"
r = requests.post(url)
print(r.status_code)
print(r.text)

import jwt
import time

# Load your GitHub App’s private key
private_key = """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

app_id = 1605045  # from GitHub App settings

now = int(time.time())
payload = {
    # issued at time
    "iat": now,
    # JWT expiration time (max 10 minutes)
    "exp": now + 600,
    # GitHub App's identifier
    "iss": app_id
}

jwt_token = jwt.encode(payload, private_key, algorithm="RS256")
print(jwt_token)


import requests

installation_id = ********
url = f"https://api.github.com/app/installations/{installation_id}/access_tokens"

headers = {
    "Authorization": f"Bearer {jwt_token}",
    "Accept": "application/vnd.github+json"
}

r = requests.post(url, headers=headers)
print(r.status_code)
print(r.json())


import jwt
import time
import requests

APP_ID = 1605045      # <-- GitHub App ID (integer from settings, not installation ID)
INSTALLATION_ID = ********
PRIVATE_KEY_PATH = "key.pem"

# Load private key
with open(PRIVATE_KEY_PATH, "r") as f:
    private_key = f.read()

# Create JWT
now = int(time.time())
payload = {
    "iat": now - 60,
    "exp": now + 600,  # 10 minutes max
    "iss": APP_ID,
}

jwt_token = jwt.encode(payload, private_key, algorithm="RS256")
if isinstance(jwt_token, bytes):
    jwt_token = jwt_token.decode("utf-8")

# Exchange JWT for installation token
url = f"https://api.github.com/app/installations/{INSTALLATION_ID}/access_tokens"
headers = {
    "Authorization": f"Bearer {jwt_token}",
    "Accept": "application/vnd.github+json"
}
r = requests.post(url, headers=headers)

print("Status:", r.status_code)
print(r.json())


jwt_token

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.serialization import load_pem_private_key

# Load the PKCS#1 private key
with open("key.pem", "rb") as f:
    private_key_data = f.read()

# Parse the private key
private_key = load_pem_private_key(private_key_data, password=None)

# Convert to PKCS#8 format
pkcs8_private_key = private_key.private_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PrivateFormat.PKCS8,
    encryption_algorithm=serialization.NoEncryption()
)

# Use this for JWT encoding
jwt_token = jwt.encode(payload, pkcs8_private_key, algorithm="RS256")

jwt_token

import jwt
import time
import requests
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.serialization import load_pem_private_key

APP_ID = 1605045
INSTALLATION_ID = ********

# Load and convert private key
with open("key.pem", "rb") as f:
    private_key_data = f.read()

# Parse the private key (handles both PKCS#1 and PKCS#8)
private_key_obj = load_pem_private_key(private_key_data, password=None)

# Convert to PKCS#8 PEM format (what PyJWT expects)
private_key_pem = private_key_obj.private_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PrivateFormat.PKCS8,
    encryption_algorithm=serialization.NoEncryption()
)

# Create JWT
now = int(time.time())
payload = {
    "iat": now - 60,    # Issued 60 seconds ago to account for clock skew
    "exp": now + 600,   # Expires in 10 minutes (GitHub's max)
    "iss": APP_ID,      # GitHub App ID
}

# Generate JWT token
jwt_token = jwt.encode(payload, private_key_pem, algorithm="RS256")

# Exchange JWT for installation access token
url = f"https://api.github.com/app/installations/{INSTALLATION_ID}/access_tokens"
headers = {
    "Authorization": f"Bearer ****************************************",
    "Accept": "application/vnd.github.v3.diff",
}

response = requests.post(url, headers=headers)
print("Status:", response.status_code)
print("Response:", response.json())

url = "http://localhost:3001/api/github/token/********"
response = requests.get(url)
print("Status:", response.status_code)
print("Response:", response.json())

